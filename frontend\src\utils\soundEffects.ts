// Sound effects utility for the gaming lounge application

class SoundEffects {
  private audioContext: AudioContext | null = null;
  private enabled: boolean = true;

  constructor() {
    // Initialize audio context on user interaction
    this.initAudioContext();
  }

  private initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported');
      this.enabled = false;
    }
  }

  private async ensureAudioContext() {
    if (!this.audioContext || !this.enabled) return false;
    
    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('Could not resume audio context');
        return false;
      }
    }
    return true;
  }

  // Create a beep sound with specified frequency and duration
  private async createBeep(frequency: number, duration: number, volume: number = 0.1) {
    if (!(await this.ensureAudioContext()) || !this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  // Success sound - ascending notes
  async playSuccess() {
    if (!this.enabled) return;
    
    const notes = [523.25, 659.25, 783.99]; // C5, E5, G5
    for (let i = 0; i < notes.length; i++) {
      setTimeout(() => {
        this.createBeep(notes[i], 0.2, 0.1);
      }, i * 100);
    }
  }

  // Error sound - descending notes
  async playError() {
    if (!this.enabled) return;
    
    const notes = [783.99, 659.25, 523.25]; // G5, E5, C5
    for (let i = 0; i < notes.length; i++) {
      setTimeout(() => {
        this.createBeep(notes[i], 0.3, 0.15);
      }, i * 150);
    }
  }

  // Click sound - short beep
  async playClick() {
    if (!this.enabled) return;
    this.createBeep(800, 0.1, 0.05);
  }

  // Hover sound - subtle beep
  async playHover() {
    if (!this.enabled) return;
    this.createBeep(1000, 0.05, 0.03);
  }

  // Notification sound - attention-grabbing
  async playNotification() {
    if (!this.enabled) return;
    
    const pattern = [659.25, 523.25, 659.25, 523.25]; // E5, C5, E5, C5
    for (let i = 0; i < pattern.length; i++) {
      setTimeout(() => {
        this.createBeep(pattern[i], 0.15, 0.08);
      }, i * 200);
    }
  }

  // Cyberpunk-style startup sound
  async playStartup() {
    if (!this.enabled) return;
    
    // Sweep from low to high frequency
    if (!(await this.ensureAudioContext()) || !this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.type = 'sawtooth';
    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800, this.audioContext.currentTime + 1);

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 1);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + 1);
  }

  // Enable/disable sound effects
  setEnabled(enabled: boolean) {
    this.enabled = enabled;
    if (enabled && !this.audioContext) {
      this.initAudioContext();
    }
  }

  isEnabled(): boolean {
    return this.enabled;
  }
}

// Create a singleton instance
const soundEffects = new SoundEffects();

export default soundEffects;
