// Mock data service for demo purposes when database is not available

let mockPCs = [
  {
    id: 1,
    pc_number: 1,
    name: 'Gaming Beast 01',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    pc_number: 2,
    name: 'Gaming Beast 02',
    status: 'in_use',
    current_status: 'in_use',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    customer_name: '<PERSON>',
    start_time: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
    end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    pc_number: 3,
    name: 'Gaming Beast 03',
    status: 'reserved',
    current_status: 'reserved',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    customer_name: 'Jane Smith',
    start_time: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
    end_time: new Date(Date.now() + 3.5 * 60 * 60 * 1000).toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    pc_number: 4,
    name: 'Gaming Beast 04',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 5,
    pc_number: 5,
    name: 'Gaming Beast 05',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 6,
    pc_number: 6,
    name: 'Gaming Beast 06',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 7,
    pc_number: 7,
    name: 'Gaming Beast 07',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 8,
    pc_number: 8,
    name: 'Gaming Beast 08',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 9,
    pc_number: 9,
    name: 'Gaming Beast 09',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 10,
    pc_number: 10,
    name: 'Gaming Beast 10',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 11,
    pc_number: 11,
    name: 'Gaming Beast 11',
    status: 'available',
    current_status: 'available',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 12,
    pc_number: 12,
    name: 'Gaming Beast 12',
    status: 'maintenance',
    current_status: 'maintenance',
    specifications: {
      cpu: 'Intel i9-13900K',
      gpu: 'RTX 4080',
      ram: '32GB DDR5',
      storage: '2TB NVMe SSD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

let mockReservations = [
  {
    id: 1,
    pc_id: 2,
    pc_number: 2,
    pc_name: 'Gaming Beast 02',
    customer_name: 'John Doe',
    customer_email: '<EMAIL>',
    customer_phone: '******-0101',
    start_time: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
    end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
    duration_hours: 3,
    status: 'confirmed',
    total_amount: 45.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    pc_id: 3,
    pc_number: 3,
    pc_name: 'Gaming Beast 03',
    customer_name: 'Jane Smith',
    customer_email: '<EMAIL>',
    customer_phone: '******-0102',
    start_time: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
    end_time: new Date(Date.now() + 3.5 * 60 * 60 * 1000).toISOString(),
    duration_hours: 3,
    status: 'confirmed',
    total_amount: 45.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

let mockAdmin = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  password_hash: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' // admin123
};

let nextReservationId = 3;

const mockDataService = {
  // PC operations
  getAllPCs: () => {
    return { success: true, data: mockPCs };
  },

  getPCById: (id) => {
    const pc = mockPCs.find(p => p.id === parseInt(id));
    return { success: true, data: pc || null };
  },

  updatePCStatus: (id, status) => {
    const pcIndex = mockPCs.findIndex(p => p.id === parseInt(id));
    if (pcIndex !== -1) {
      mockPCs[pcIndex].status = status;
      mockPCs[pcIndex].current_status = status;
      mockPCs[pcIndex].updated_at = new Date().toISOString();
      return { success: true, affectedRows: 1 };
    }
    return { success: true, affectedRows: 0 };
  },

  // Reservation operations
  getAllReservations: () => {
    return { success: true, data: mockReservations };
  },

  getReservationById: (id) => {
    const reservation = mockReservations.find(r => r.id === parseInt(id));
    return { success: true, data: reservation || null };
  },

  createReservation: (reservationData) => {
    const newReservation = {
      id: nextReservationId++,
      ...reservationData,
      pc_number: mockPCs.find(p => p.id === reservationData.pc_id)?.pc_number,
      pc_name: mockPCs.find(p => p.id === reservationData.pc_id)?.name,
      status: 'confirmed',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockReservations.push(newReservation);
    return { success: true, insertId: newReservation.id };
  },

  // Admin operations
  getAdminByUsername: (username) => {
    if (username === mockAdmin.username) {
      return { success: true, data: mockAdmin };
    }
    return { success: true, data: null };
  },

  // Dashboard stats
  getDashboardStats: () => {
    const stats = {
      totalPCs: mockPCs.length,
      availablePCs: mockPCs.filter(pc => pc.status === 'available').length,
      inUsePCs: mockPCs.filter(pc => pc.status === 'in_use').length,
      maintenancePCs: mockPCs.filter(pc => pc.status === 'maintenance').length,
      todayReservations: mockReservations.length,
      activeReservations: mockReservations.filter(r => r.status === 'confirmed').length,
      upcomingReservations: mockReservations.filter(r => new Date(r.start_time) > new Date()).length,
      todayRevenue: mockReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    };

    return {
      success: true,
      data: {
        statistics: stats,
        recentReservations: mockReservations.slice(-10),
        pcStatus: mockPCs
      }
    };
  }
};

module.exports = mockDataService;
