const express = require('express');
const router = express.Router();
const { executeQuery, getOne, insert, update, deleteRecord } = require('../config/database');
const mockDataService = require('../services/mockData');

// Get all reservations
router.get('/', async (req, res) => {
  try {
    const { status, pc_id, date, limit = 50, offset = 0 } = req.query;
    
    let query = `
      SELECT 
        r.*,
        p.pc_number,
        p.name as pc_name
      FROM reservations r
      JOIN pcs p ON r.pc_id = p.id
      WHERE 1=1
    `;
    const params = [];

    if (status) {
      query += ' AND r.status = ?';
      params.push(status);
    }

    if (pc_id) {
      query += ' AND r.pc_id = ?';
      params.push(pc_id);
    }

    if (date) {
      query += ' AND DATE(r.start_time) = ?';
      params.push(date);
    }

    query += ' ORDER BY r.start_time DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const result = await executeQuery(query, params);
    
    if (!result.success) {
      return res.status(500).json({ error: 'Failed to fetch reservations' });
    }

    res.json({
      success: true,
      data: result.data,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: result.data.length
      }
    });
  } catch (error) {
    console.error('Error fetching reservations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get a specific reservation
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT 
        r.*,
        p.pc_number,
        p.name as pc_name,
        p.specifications
      FROM reservations r
      JOIN pcs p ON r.pc_id = p.id
      WHERE r.id = ?
    `;

    const result = await getOne(query, [id]);
    
    if (!result.success) {
      return res.status(500).json({ error: 'Failed to fetch reservation' });
    }

    if (!result.data) {
      return res.status(404).json({ error: 'Reservation not found' });
    }

    res.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    console.error('Error fetching reservation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create a new reservation
router.post('/', async (req, res) => {
  try {
    const {
      pc_id,
      customer_name,
      customer_email,
      customer_phone,
      start_time,
      end_time,
      duration_hours,
      notes
    } = req.body;

    // Validation
    if (!pc_id || !customer_name || !customer_email || !start_time || !end_time || !duration_hours) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Calculate total amount (assuming $15/hour base rate)
    const total_amount = duration_hours * 15.00;

    try {
      // Try database first
      const pcResult = await getOne('SELECT * FROM pcs WHERE id = ? AND status != "maintenance"', [pc_id]);
      if (!pcResult.success || !pcResult.data) {
        return res.status(400).json({ error: 'PC not available' });
      }

      // Check for conflicting reservations
      const conflictQuery = `
        SELECT COUNT(*) as conflicts
        FROM reservations
        WHERE pc_id = ?
          AND status IN ('confirmed', 'pending')
          AND (
            (start_time <= ? AND end_time > ?) OR
            (start_time < ? AND end_time >= ?) OR
            (start_time >= ? AND end_time <= ?)
          )
      `;

      const conflictResult = await getOne(conflictQuery, [
        pc_id, start_time, start_time, end_time, end_time, start_time, end_time
      ]);

      if (!conflictResult.success) {
        return res.status(500).json({ error: 'Failed to check availability' });
      }

      if (conflictResult.data.conflicts > 0) {
        return res.status(400).json({ error: 'Time slot not available' });
      }

      // Insert reservation
      const insertQuery = `
        INSERT INTO reservations (
          pc_id, customer_name, customer_email, customer_phone,
          start_time, end_time, duration_hours, total_amount, notes, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')
      `;

      const insertResult = await insert(insertQuery, [
        pc_id, customer_name, customer_email, customer_phone,
        start_time, end_time, duration_hours, total_amount, notes
      ]);

      if (!insertResult.success) {
        return res.status(500).json({ error: 'Failed to create reservation' });
      }

      // Get the created reservation with PC details
      const newReservation = await getOne(`
        SELECT
          r.*,
          p.pc_number,
          p.name as pc_name
        FROM reservations r
        JOIN pcs p ON r.pc_id = p.id
        WHERE r.id = ?
      `, [insertResult.insertId]);

      // Broadcast update to all connected clients
      if (global.broadcastReservationUpdate && newReservation.success) {
        global.broadcastReservationUpdate(newReservation.data);
      }

      return res.status(201).json({
        success: true,
        message: 'Reservation created successfully',
        data: newReservation.data
      });
    } catch (dbError) {
      console.log('Database not available, using mock data');
    }

    // Use mock data
    const reservationData = {
      pc_id: parseInt(pc_id),
      customer_name,
      customer_email,
      customer_phone,
      start_time,
      end_time,
      duration_hours: parseFloat(duration_hours),
      total_amount,
      notes
    };

    const mockResult = mockDataService.createReservation(reservationData);
    const newReservation = mockDataService.getReservationById(mockResult.insertId);

    // Broadcast update to all connected clients
    if (global.broadcastReservationUpdate && newReservation.success) {
      global.broadcastReservationUpdate(newReservation.data);
    }

    res.status(201).json({
      success: true,
      message: 'Reservation created successfully',
      data: newReservation.data
    });
  } catch (error) {
    console.error('Error creating reservation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update reservation status
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const result = await update(
      'UPDATE reservations SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );

    if (!result.success) {
      return res.status(500).json({ error: 'Failed to update reservation status' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Reservation not found' });
    }

    // Get updated reservation data
    const updatedReservation = await getOne(`
      SELECT 
        r.*,
        p.pc_number,
        p.name as pc_name
      FROM reservations r
      JOIN pcs p ON r.pc_id = p.id
      WHERE r.id = ?
    `, [id]);
    
    // Broadcast update to all connected clients
    if (global.broadcastReservationUpdate && updatedReservation.success) {
      global.broadcastReservationUpdate(updatedReservation.data);
    }

    res.json({
      success: true,
      message: 'Reservation status updated successfully',
      data: updatedReservation.data
    });
  } catch (error) {
    console.error('Error updating reservation status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Cancel reservation
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get reservation details before deletion
    const reservation = await getOne('SELECT * FROM reservations WHERE id = ?', [id]);
    if (!reservation.success || !reservation.data) {
      return res.status(404).json({ error: 'Reservation not found' });
    }

    // Check if reservation can be cancelled (not already completed)
    if (reservation.data.status === 'completed') {
      return res.status(400).json({ error: 'Cannot cancel completed reservation' });
    }

    const result = await update(
      'UPDATE reservations SET status = "cancelled", updated_at = NOW() WHERE id = ?',
      [id]
    );

    if (!result.success) {
      return res.status(500).json({ error: 'Failed to cancel reservation' });
    }

    // Broadcast update to all connected clients
    if (global.broadcastReservationUpdate) {
      global.broadcastReservationUpdate({
        ...reservation.data,
        status: 'cancelled'
      });
    }

    res.json({
      success: true,
      message: 'Reservation cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling reservation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
