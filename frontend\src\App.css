/* Gaming Lounge Cyberpunk Theme */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

:root {
  /* Cyberpunk Color Palette */
  --primary-bg: #0a0a0f;
  --secondary-bg: #1a1a2e;
  --accent-bg: #16213e;
  --card-bg: rgba(26, 26, 46, 0.8);
  --modal-bg: rgba(10, 10, 15, 0.95);
  
  /* Neon Colors */
  --neon-blue: #00d4ff;
  --neon-purple: #b300ff;
  --neon-green: #00ff88;
  --neon-pink: #ff0080;
  --neon-yellow: #ffff00;
  --neon-orange: #ff6600;
  
  /* Status Colors */
  --status-available: var(--neon-green);
  --status-in-use: #ff4444;
  --status-reserved: var(--neon-yellow);
  --status-maintenance: var(--neon-orange);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-muted: #666666;
  --text-accent: var(--neon-blue);
  
  /* Fonts */
  --font-primary: 'Rajdhani', sans-serif;
  --font-accent: 'Orbitron', monospace;
  
  /* Shadows and Glows */
  --glow-blue: 0 0 20px rgba(0, 212, 255, 0.5);
  --glow-purple: 0 0 20px rgba(179, 0, 255, 0.5);
  --glow-green: 0 0 20px rgba(0, 255, 136, 0.5);
  --glow-pink: 0 0 20px rgba(255, 0, 128, 0.5);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* App Container */
.app {
  min-height: 100vh;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(179, 0, 255, 0.1) 0%, transparent 50%),
    var(--primary-bg);
  position: relative;
}

.app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.03) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(179, 0, 255, 0.03) 50%, transparent 100%);
  pointer-events: none;
  z-index: -1;
}

/* Loading States */
.app.loading, .app.error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-spinner, .error-message {
  text-align: center;
  padding: 2rem;
  background: var(--card-bg);
  border-radius: 12px;
  border: 2px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.cyber-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid transparent;
  border-top: 3px solid var(--neon-blue);
  border-right: 3px solid var(--neon-purple);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header Styles */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--modal-bg);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid var(--neon-blue);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left .logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: var(--font-accent);
  font-weight: 900;
  font-size: 1.5rem;
  color: var(--neon-blue);
}

.logo-icon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

.header-center .datetime-display {
  text-align: center;
}

.time-display .time {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  color: var(--neon-green);
  text-shadow: var(--glow-green);
}

.date-display .date {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.admin-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mode-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--secondary-bg);
  border: 2px solid var(--neon-purple);
  border-radius: 6px;
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.mode-toggle:hover {
  background: var(--neon-purple);
  box-shadow: var(--glow-purple);
  transform: translateY(-2px);
}

.logout-button {
  padding: 0.5rem;
  background: var(--secondary-bg);
  border: 2px solid var(--neon-pink);
  border-radius: 6px;
  color: var(--neon-pink);
  transition: all var(--transition-normal);
}

.logout-button:hover {
  background: var(--neon-pink);
  color: var(--primary-bg);
  box-shadow: var(--glow-pink);
}

/* Main Content */
.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.hero-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.hero-title {
  font-family: var(--font-accent);
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 4px;
}

.cyber-text {
  color: var(--neon-blue);
  text-shadow: var(--glow-blue);
  animation: glow 2s ease-in-out infinite alternate;
}

.zone-text {
  color: var(--neon-purple);
  text-shadow: var(--glow-purple);
  animation: glow 2s ease-in-out infinite alternate-reverse;
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  }
  to {
    text-shadow: 0 0 20px currentColor, 0 0 30px currentColor, 0 0 40px currentColor;
  }
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 300;
}

/* PC Grid Styles */
.pc-grid-container {
  margin-bottom: 2rem;
}

.section-title {
  font-family: var(--font-accent);
  font-size: 2rem;
  color: var(--neon-blue);
  text-align: center;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.status-overview {
  margin-bottom: 3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all var(--transition-normal);
}

.status-card.available {
  border-color: var(--status-available);
  box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
}

.status-card.in-use {
  border-color: var(--status-in-use);
  box-shadow: 0 4px 20px rgba(255, 68, 68, 0.2);
}

.status-card.reserved {
  border-color: var(--status-reserved);
  box-shadow: 0 4px 20px rgba(255, 255, 0, 0.2);
}

.status-card.maintenance {
  border-color: var(--status-maintenance);
  box-shadow: 0 4px 20px rgba(255, 102, 0, 0.2);
}

.status-icon {
  font-size: 2rem;
}

.status-count {
  font-family: var(--font-accent);
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  display: block;
}

.status-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pc-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* PC Card Styles */
.pc-card {
  background: var(--card-bg);
  border-radius: 12px;
  border: 2px solid transparent;
  padding: 1.5rem;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.pc-card.available {
  border-color: var(--status-available);
  box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
}

.pc-card.in_use {
  border-color: var(--status-in-use);
  box-shadow: 0 4px 20px rgba(255, 68, 68, 0.2);
}

.pc-card.reserved {
  border-color: var(--status-reserved);
  box-shadow: 0 4px 20px rgba(255, 255, 0, 0.2);
}

.pc-card.maintenance {
  border-color: var(--status-maintenance);
  box-shadow: 0 4px 20px rgba(255, 102, 0, 0.2);
}

.pc-card.clickable {
  cursor: pointer;
}

.pc-card.clickable:hover {
  transform: translateY(-5px);
  box-shadow:
    0 8px 30px rgba(0, 255, 136, 0.4),
    0 0 20px var(--neon-blue);
}

.pc-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pc-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: var(--font-accent);
  font-weight: 700;
}

.pc-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.pc-num {
  font-size: 1.5rem;
  color: var(--neon-blue);
  text-shadow: var(--glow-blue);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-indicator.available {
  background: rgba(0, 255, 136, 0.2);
  color: var(--status-available);
  border: 1px solid var(--status-available);
}

.status-indicator.in_use {
  background: rgba(255, 68, 68, 0.2);
  color: var(--status-in-use);
  border: 1px solid var(--status-in-use);
}

.status-indicator.reserved {
  background: rgba(255, 255, 0, 0.2);
  color: var(--status-reserved);
  border: 1px solid var(--status-reserved);
}

.status-indicator.maintenance {
  background: rgba(255, 102, 0, 0.2);
  color: var(--status-maintenance);
  border: 1px solid var(--status-maintenance);
}

.pc-name {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pc-specs {
  margin-bottom: 1rem;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.spec-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.spec-value {
  color: var(--text-primary);
  font-weight: 600;
}

.reservation-info {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 3px solid var(--neon-blue);
}

.customer-name, .time-slot {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.customer-name .label, .time-slot .label {
  color: var(--text-secondary);
}

.customer-name .value, .time-slot .value {
  color: var(--text-primary);
  font-weight: 600;
}

.action-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
  border-radius: 8px;
  color: var(--primary-bg);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: pulse 2s infinite;
}

.maintenance-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background: rgba(255, 102, 0, 0.2);
  border: 1px solid var(--status-maintenance);
  border-radius: 8px;
  color: var(--status-maintenance);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pc-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.8rem;
  color: var(--text-muted);
}

.hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.pc-card.clickable:hover .hover-effect {
  opacity: 1;
}

.glow-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-green), var(--neon-purple), var(--neon-pink));
  border-radius: 12px;
  z-index: -1;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pulse-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, var(--neon-blue), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-expand 2s ease-out infinite;
}

@keyframes pulse-expand {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .pc-grid {
    grid-template-columns: 1fr;
  }

  .status-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.reservation-modal {
  background: var(--modal-bg);
  border: 2px solid var(--neon-blue);
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--glow-blue);
  animation: slideInUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.close-button:hover {
  background: var(--neon-pink);
  color: var(--primary-bg);
  box-shadow: var(--glow-pink);
}

.modal-body {
  padding: 1.5rem;
}

.pc-info {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 3px solid var(--neon-blue);
}

.pc-info h3 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
}

.pc-specs-summary {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.pc-specs-summary span {
  margin-right: 0.5rem;
}

/* Form Styles */
.reservation-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  background: var(--secondary-bg);
  border: 2px solid transparent;
  color: var(--text-primary);
  padding: 12px 16px;
  border-radius: 6px;
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.form-group input.readonly {
  background: rgba(0, 0, 0, 0.3);
  color: var(--text-muted);
  cursor: not-allowed;
}

.price-summary {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid var(--neon-green);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.price-calculation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.total-price {
  font-family: var(--font-accent);
  font-size: 1.2rem;
  color: var(--neon-green);
  text-shadow: var(--glow-green);
}

.error-message {
  background: rgba(255, 68, 68, 0.2);
  border: 1px solid var(--status-in-use);
  border-radius: 6px;
  padding: 1rem;
  color: var(--status-in-use);
  font-weight: 600;
  text-align: center;
  margin: 1rem 0;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.cancel-button {
  background: var(--secondary-bg);
  border: 2px solid var(--text-muted);
  color: var(--text-primary);
  padding: 12px 24px;
  border-radius: 6px;
  font-family: var(--font-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.cancel-button:hover {
  border-color: var(--neon-pink);
  color: var(--neon-pink);
  box-shadow: var(--glow-pink);
}

.submit-button {
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
  border: none;
  color: var(--primary-bg);
  padding: 12px 24px;
  border-radius: 6px;
  font-family: var(--font-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 255, 136, 0.4);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .reservation-modal {
    width: 95%;
    margin: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }
}

/* Admin Login Styles */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.cyber-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.floating-particles::before,
.floating-particles::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--neon-blue);
  border-radius: 50%;
  animation: float-particle 8s ease-in-out infinite;
}

.floating-particles::before {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-particles::after {
  top: 60%;
  right: 15%;
  animation-delay: 4s;
}

@keyframes float-particle {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
  50% { transform: translateY(-100px) rotate(180deg); opacity: 0.5; }
}

.login-card {
  background: var(--modal-bg);
  border: 2px solid var(--neon-blue);
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: var(--glow-blue);
  backdrop-filter: blur(10px);
  animation: slideInUp 0.5s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.login-title {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.login-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-label {
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
  z-index: 1;
}

.form-input {
  width: 100%;
  background: var(--secondary-bg);
  border: 2px solid transparent;
  color: var(--text-primary);
  padding: 12px 16px 12px 40px;
  border-radius: 6px;
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all var(--transition-normal);
}

.password-toggle:hover {
  color: var(--neon-blue);
}

.login-button {
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
  border: none;
  color: var(--text-primary);
  padding: 15px 24px;
  border-radius: 6px;
  font-family: var(--font-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-blue);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.login-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.demo-credentials {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  border-left: 3px solid var(--neon-green);
}

.demo-credentials h4 {
  color: var(--neon-green);
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.demo-credentials p {
  color: var(--text-secondary);
  margin: 0.25rem 0;
  font-size: 0.8rem;
}

.login-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.glow-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

.glow-orb-1 {
  width: 200px;
  height: 200px;
  background: var(--neon-blue);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.glow-orb-2 {
  width: 150px;
  height: 150px;
  background: var(--neon-purple);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.glow-orb-3 {
  width: 100px;
  height: 100px;
  background: var(--neon-green);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@media (max-width: 480px) {
  .status-cards {
    grid-template-columns: 1fr;
  }

  .hero-title {
    font-size: 2rem;
    letter-spacing: 2px;
  }

  .login-card {
    margin: 1rem;
    padding: 1.5rem;
  }
}

/* Admin Dashboard Styles */
.admin-dashboard {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.admin-dashboard.loading,
.admin-dashboard.error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.dashboard-tabs {
  display: flex;
  gap: 1rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.tab {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 1rem 1.5rem;
  font-family: var(--font-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all var(--transition-normal);
  border-bottom: 2px solid transparent;
}

.tab.active {
  color: var(--neon-blue);
  border-bottom-color: var(--neon-blue);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.tab:hover {
  color: var(--text-primary);
}

.dashboard-overview {
  animation: slideInUp 0.5s ease;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--card-bg);
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);
}

.stat-card.available {
  border-color: var(--status-available);
  box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
}

.stat-card.in-use {
  border-color: var(--status-in-use);
  box-shadow: 0 4px 20px rgba(255, 68, 68, 0.2);
}

.stat-card.maintenance {
  border-color: var(--status-maintenance);
  box-shadow: 0 4px 20px rgba(255, 102, 0, 0.2);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.stat-value {
  font-family: var(--font-accent);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  font-weight: 600;
}

.recent-activity {
  background: var(--card-bg);
  border: 2px solid var(--neon-blue);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.recent-activity h3 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1rem;
}

.reservations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reservation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border-left: 3px solid var(--neon-green);
}

.reservation-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-name {
  font-weight: 600;
  color: var(--text-primary);
}

.pc-info {
  color: var(--neon-blue);
  font-size: 0.9rem;
}

.time-info {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-badge.confirmed {
  background: rgba(0, 255, 136, 0.2);
  color: var(--status-available);
  border: 1px solid var(--status-available);
}

.status-badge.pending {
  background: rgba(255, 255, 0, 0.2);
  color: var(--status-reserved);
  border: 1px solid var(--status-reserved);
}

.status-badge.completed {
  background: rgba(0, 212, 255, 0.2);
  color: var(--neon-blue);
  border: 1px solid var(--neon-blue);
}

.status-badge.cancelled {
  background: rgba(255, 68, 68, 0.2);
  color: var(--status-in-use);
  border: 1px solid var(--status-in-use);
}

/* PC Management Styles */
.pc-management {
  animation: slideInUp 0.5s ease;
}

.pc-management h3 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
}

.pc-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.pc-management-card {
  background: var(--card-bg);
  border: 2px solid var(--neon-blue);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pc-header h4 {
  font-family: var(--font-accent);
  color: var(--text-primary);
  margin: 0;
  text-transform: uppercase;
}

.pc-controls {
  margin-bottom: 1rem;
}

.status-select {
  width: 100%;
  background: var(--secondary-bg);
  border: 2px solid var(--neon-blue);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  font-family: var(--font-primary);
  cursor: pointer;
}

.current-user {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-style: italic;
}

/* Reservations Management */
.reservations-management {
  animation: slideInUp 0.5s ease;
}

.reservations-management h3 {
  font-family: var(--font-accent);
  color: var(--neon-blue);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
}

.reservations-table {
  background: var(--card-bg);
  border: 2px solid var(--neon-blue);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 212, 255, 0.1);
  border-bottom: 1px solid var(--neon-blue);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--neon-blue);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal);
}

.table-row:hover {
  background: rgba(0, 212, 255, 0.05);
}

.table-row:last-child {
  border-bottom: none;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pc-management-grid {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .dashboard-tabs {
    flex-wrap: wrap;
  }
}
