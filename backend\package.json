{"name": "gamebb-backend", "version": "1.0.0", "description": "Gaming Lounge Reservation System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["gaming", "reservation", "lounge", "api"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "socket.io": "^4.7.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}}