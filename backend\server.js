const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { testConnection } = require('./config/database');

// Import routes
const pcRoutes = require('./routes/pcs');
const reservationRoutes = require('./routes/reservations');
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const userRoutes = require('./routes/users');

const app = express();
const server = http.createServer(app);

// Socket.io setup
const io = socketIo(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Make io accessible to routes
app.use((req, res, next) => {
  req.io = io;
  next();
});

// Routes
app.use('/api/pcs', pcRoutes);
app.use('/api/reservations', reservationRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/users', userRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Gaming Lounge Reservation System API',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      pcs: '/api/pcs',
      reservations: '/api/reservations',
      auth: '/api/auth',
      admin: '/api/admin',
      users: '/api/users'
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('🔌 Client connected:', socket.id);

  // Join admin room for admin-specific updates
  socket.on('join-admin', () => {
    socket.join('admin');
    console.log('👑 Admin joined:', socket.id);
  });

  // Leave admin room
  socket.on('leave-admin', () => {
    socket.leave('admin');
    console.log('👑 Admin left:', socket.id);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('🔌 Client disconnected:', socket.id);
  });
});

// Broadcast functions for real-time updates
const broadcastPCUpdate = (pcData) => {
  io.emit('pc-status-update', pcData);
};

const broadcastReservationUpdate = (reservationData) => {
  io.emit('reservation-update', reservationData);
  io.to('admin').emit('admin-reservation-update', reservationData);
};

// Make broadcast functions available globally
global.broadcastPCUpdate = broadcastPCUpdate;
global.broadcastReservationUpdate = broadcastReservationUpdate;

// Start server
const PORT = process.env.PORT || 3001;

async function startServer() {
  try {
    // Test database connection (optional for now)
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.warn('⚠️ Database not connected. Running in demo mode...');
    }

    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
      if (!dbConnected) {
        console.log('📝 Note: Database not connected. Some features may not work.');
      }
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received. Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
