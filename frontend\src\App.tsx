import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import PCGrid from './components/PCGrid';
import Header from './components/Header';
import ReservationModal from './components/ReservationModal';
import AdminLogin from './components/AdminLogin';
import AdminDashboard from './components/AdminDashboard';
import NotificationSystem, { useNotifications } from './components/NotificationSystem';
import { PC, Reservation } from './types';
import soundEffects from './utils/soundEffects';
import './App.css';

const API_BASE_URL = 'http://localhost:3001';

function App() {
  const [pcs, setPCs] = useState<PC[]>([]);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [selectedPC, setSelectedPC] = useState<PC | null>(null);
  const [showReservationModal, setShowReservationModal] = useState(false);
  const [isAdminMode, setIsAdminMode] = useState(false);
  const [adminToken, setAdminToken] = useState<string | null>(
    localStorage.getItem('adminToken')
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Notification system
  const {
    notifications,
    removeNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  } = useNotifications();

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io(API_BASE_URL);
    setSocket(newSocket);

    // Listen for real-time updates
    newSocket.on('pc-status-update', (updatedPC: PC) => {
      setPCs(prevPCs => 
        prevPCs.map(pc => pc.id === updatedPC.id ? updatedPC : pc)
      );
    });

    newSocket.on('reservation-update', (reservation: Reservation) => {
      // Refresh PCs when reservations change
      fetchPCs();
    });

    return () => {
      newSocket.close();
    };
  }, []);

  // Fetch PCs data
  const fetchPCs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/pcs`);
      const data = await response.json();
      
      if (data.success) {
        setPCs(data.data);
      } else {
        setError('Failed to fetch PCs data');
      }
    } catch (err) {
      setError('Failed to connect to server');
      console.error('Error fetching PCs:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPCs();
  }, []);

  // Handle PC selection for reservation
  const handlePCSelect = (pc: PC) => {
    if (pc.current_status === 'available') {
      soundEffects.playClick();
      setSelectedPC(pc);
      setShowReservationModal(true);
      showInfo('PC Selected', `Selected ${pc.name} for reservation`);
    } else {
      soundEffects.playError();
      showWarning('PC Unavailable', `${pc.name} is currently ${pc.current_status}`);
    }
  };

  // Handle successful reservation
  const handleReservationSuccess = () => {
    soundEffects.playSuccess();
    showSuccess('Reservation Created', 'Your gaming session has been reserved successfully!');
    setShowReservationModal(false);
    setSelectedPC(null);
    fetchPCs(); // Refresh data
  };

  // Handle admin login
  const handleAdminLogin = (token: string) => {
    soundEffects.playSuccess();
    showSuccess('Admin Login', 'Successfully logged in as administrator');
    setAdminToken(token);
    localStorage.setItem('adminToken', token);
    setIsAdminMode(true);
  };

  // Handle admin logout
  const handleAdminLogout = () => {
    setAdminToken(null);
    localStorage.removeItem('adminToken');
    setIsAdminMode(false);
  };

  // Toggle admin mode
  const toggleAdminMode = () => {
    if (adminToken) {
      setIsAdminMode(!isAdminMode);
    }
  };

  if (loading) {
    return (
      <div className="app loading">
        <div className="loading-spinner">
          <div className="cyber-spinner"></div>
          <p>Initializing Gaming Lounge...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app error">
        <div className="error-message">
          <h2>Connection Error</h2>
          <p>{error}</p>
          <button onClick={fetchPCs} className="retry-button">
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <Header
        isAdminMode={isAdminMode}
        onToggleAdminMode={toggleAdminMode}
        onAdminLogout={handleAdminLogout}
        hasAdminToken={!!adminToken}
      />

      {!adminToken ? (
        <AdminLogin onLogin={handleAdminLogin} />
      ) : isAdminMode ? (
        <AdminDashboard
          adminToken={adminToken}
          onLogout={handleAdminLogout}
        />
      ) : (
        <>
          <main className="main-content">
            <div className="hero-section">
              <h1 className="hero-title">
                <span className="cyber-text">CYBER</span>
                <span className="zone-text">ZONE</span>
              </h1>
              <p className="hero-subtitle">
                Premium Gaming Experience • 12 High-Performance PCs
              </p>
            </div>

            <PCGrid
              pcs={pcs}
              onPCSelect={handlePCSelect}
            />
          </main>

          {showReservationModal && selectedPC && (
            <ReservationModal
              pc={selectedPC}
              onClose={() => {
                setShowReservationModal(false);
                setSelectedPC(null);
              }}
              onSuccess={handleReservationSuccess}
            />
          )}
        </>
      )}

      {/* Notification System */}
      <NotificationSystem
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  );
}

export default App;
