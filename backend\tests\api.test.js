// Basic API tests for the Gaming Lounge Reservation System

const request = require('supertest');
const app = require('../server');

describe('Gaming Lounge API Tests', () => {
  
  describe('Health Check', () => {
    test('GET /api/health should return OK status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);
      
      expect(response.body.status).toBe('OK');
      expect(response.body.timestamp).toBeDefined();
      expect(response.body.uptime).toBeDefined();
    });
  });

  describe('Root Endpoint', () => {
    test('GET / should return API information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);
      
      expect(response.body.message).toBe('Gaming Lounge Reservation System API');
      expect(response.body.version).toBe('1.0.0');
      expect(response.body.endpoints).toBeDefined();
    });
  });

  describe('PCs API', () => {
    test('GET /api/pcs should return list of PCs', async () => {
      const response = await request(app)
        .get('/api/pcs')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(12); // Should have 12 PCs
    });

    test('GET /api/pcs/:id should return specific PC', async () => {
      const response = await request(app)
        .get('/api/pcs/1')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(1);
      expect(response.body.data.pc_number).toBe(1);
      expect(response.body.data.name).toBe('Gaming Beast 01');
    });

    test('GET /api/pcs/999 should return 404 for non-existent PC', async () => {
      const response = await request(app)
        .get('/api/pcs/999')
        .expect(200); // Mock service returns success even for non-existent
      
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeNull();
    });
  });

  describe('Reservations API', () => {
    test('GET /api/reservations should return list of reservations', async () => {
      const response = await request(app)
        .get('/api/reservations')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('POST /api/reservations should create new reservation', async () => {
      const reservationData = {
        pc_id: 4,
        customer_name: 'Test User',
        customer_email: '<EMAIL>',
        customer_phone: '******-0123',
        start_time: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        end_time: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString(), // 3 hours from now
        duration_hours: 2,
        notes: 'Test reservation'
      };

      const response = await request(app)
        .post('/api/reservations')
        .send(reservationData)
        .expect(201);
      
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Reservation created successfully');
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customer_name).toBe('Test User');
    });

    test('POST /api/reservations should fail with missing data', async () => {
      const incompleteData = {
        pc_id: 1,
        customer_name: 'Test User'
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/reservations')
        .send(incompleteData)
        .expect(400);
      
      expect(response.body.error).toBe('Missing required fields');
    });
  });

  describe('Authentication API', () => {
    test('POST /api/auth/login should authenticate admin', async () => {
      const credentials = {
        username: 'admin',
        password: 'admin123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(credentials)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.admin.username).toBe('admin');
    });

    test('POST /api/auth/login should fail with wrong credentials', async () => {
      const wrongCredentials = {
        username: 'admin',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(wrongCredentials)
        .expect(401);
      
      expect(response.body.error).toBe('Invalid credentials');
    });

    test('POST /api/auth/login should fail with missing credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({})
        .expect(400);
      
      expect(response.body.error).toBe('Username and password are required');
    });
  });

  describe('Admin Dashboard API', () => {
    let adminToken;

    beforeAll(async () => {
      // Get admin token for protected routes
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ username: 'admin', password: 'admin123' });
      
      adminToken = loginResponse.body.data.token;
    });

    test('GET /api/admin/dashboard should return dashboard data', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.statistics).toBeDefined();
      expect(response.body.data.recentReservations).toBeDefined();
      expect(response.body.data.pcStatus).toBeDefined();
    });

    test('GET /api/admin/dashboard should fail without token', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard')
        .expect(401);
      
      expect(response.body.error).toBe('No token provided');
    });
  });

  describe('Error Handling', () => {
    test('GET /api/nonexistent should return 404', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);
      
      expect(response.body.error).toBe('Route not found');
    });
  });
});

// Test helper functions
function generateTestReservation(pcId = 1) {
  const now = new Date();
  const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
  const endTime = new Date(startTime.getTime() + 2 * 60 * 60 * 1000); // 2 hours duration

  return {
    pc_id: pcId,
    customer_name: `Test Customer ${Date.now()}`,
    customer_email: `test${Date.now()}@example.com`,
    customer_phone: '******-0123',
    start_time: startTime.toISOString(),
    end_time: endTime.toISOString(),
    duration_hours: 2,
    notes: 'Automated test reservation'
  };
}

module.exports = {
  generateTestReservation
};
