# Gaming Lounge Reservation System - Deployment Guide

## Overview

This is a full-stack gaming lounge reservation system with a cyberpunk-themed interface, built with React.js frontend and Node.js/Express backend.

## Current Status

✅ **Demo Mode Active** - The application is currently running in demo mode with mock data, allowing you to test all features without requiring a MySQL database setup.

## Quick Start (Demo Mode)

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 1. Start the Backend Server
```bash
cd backend
npm install
npm start
```
The backend will start on `http://localhost:3001` in demo mode.

### 2. Start the Frontend Development Server
```bash
cd frontend
npm install
npm run dev
```
The frontend will start on `http://localhost:5173`.

### 3. Access the Application
- **User Interface**: http://localhost:5173
- **Admin Login**: Use credentials `admin` / `admin123`

## Features Implemented

### ✅ Frontend Features
- **Cyberpunk Theme**: Dark mode with neon blue, purple, and green colors
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **PC Grid Layout**: 12 gaming PCs with real-time status display
- **Reservation System**: Interactive booking with date/time picker
- **Admin Dashboard**: Comprehensive management interface
- **Real-time Updates**: Socket.io integration for live status updates
- **Sound Effects**: Cyberpunk-style audio feedback
- **Notifications**: Toast notifications for user feedback
- **Live Clock**: Real-time date and time display in header

### ✅ Backend Features
- **RESTful API**: Complete CRUD operations for PCs and reservations
- **Authentication**: JWT-based admin authentication
- **Real-time Communication**: Socket.io for live updates
- **Mock Data Service**: Fallback system when database is unavailable
- **Error Handling**: Comprehensive error responses
- **CORS Support**: Cross-origin resource sharing enabled
- **Rate Limiting**: API protection against abuse

### ✅ Admin Features
- **Dashboard Statistics**: Real-time metrics and analytics
- **PC Management**: Update PC status and maintenance mode
- **Reservation Management**: View and manage all bookings
- **User Authentication**: Secure admin login system

## Production Deployment

### Database Setup (Required for Production)

1. **Install MySQL**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install mysql-server
   
   # Windows
   # Download from https://dev.mysql.com/downloads/mysql/
   
   # macOS
   brew install mysql
   ```

2. **Create Database**
   ```sql
   CREATE DATABASE gamebb;
   ```

3. **Run Database Schema**
   ```bash
   mysql -u root -p gamebb < database/schema.sql
   mysql -u root -p gamebb < database/seed_data.sql
   ```

4. **Update Environment Variables**
   ```bash
   cp backend/.env.example backend/.env
   # Edit .env with your database credentials
   ```

### Environment Configuration

#### Backend (.env)
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=gamebb
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Server Configuration
PORT=3001
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# CORS Configuration
FRONTEND_URL=https://your-domain.com
```

### Production Build

#### Frontend
```bash
cd frontend
npm run build
```

#### Backend
```bash
cd backend
npm install --production
```

### Deployment Options

#### Option 1: Traditional Server
1. Upload files to your server
2. Install Node.js and MySQL
3. Configure environment variables
4. Start services with PM2:
   ```bash
   npm install -g pm2
   pm2 start backend/server.js --name "gamebb-api"
   pm2 serve frontend/dist 5173 --name "gamebb-frontend"
   ```

#### Option 2: Docker Deployment
```dockerfile
# Dockerfile example (create in project root)
FROM node:18-alpine

WORKDIR /app

# Copy backend
COPY backend/ ./backend/
COPY database/ ./database/

# Install dependencies
RUN cd backend && npm install --production

# Copy frontend build
COPY frontend/dist/ ./frontend/dist/

EXPOSE 3001

CMD ["node", "backend/server.js"]
```

#### Option 3: Cloud Deployment
- **Vercel/Netlify**: Frontend deployment
- **Heroku/Railway**: Backend API deployment
- **PlanetScale/AWS RDS**: Database hosting

## Testing

### Run Backend Tests
```bash
cd backend
npm test
```

### Manual Testing Checklist
- [ ] PC status display and updates
- [ ] Reservation creation and validation
- [ ] Admin login and dashboard access
- [ ] Real-time updates via WebSocket
- [ ] Responsive design on mobile devices
- [ ] Sound effects and notifications
- [ ] Error handling and validation

## Security Considerations

### Production Security
1. **Change Default Credentials**: Update admin password
2. **Environment Variables**: Never commit .env files
3. **HTTPS**: Use SSL certificates in production
4. **Database Security**: Use strong passwords and limit access
5. **Rate Limiting**: Configure appropriate API limits
6. **CORS**: Restrict to your domain only

### Recommended Security Headers
```javascript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "fonts.googleapis.com"],
      fontSrc: ["'self'", "fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

## Monitoring and Maintenance

### Health Checks
- Backend: `GET /api/health`
- Database connectivity monitoring
- Real-time connection status

### Logs
- Application logs: `backend/logs/`
- Error tracking: Consider Sentry integration
- Performance monitoring: Consider New Relic

### Backup Strategy
- Database: Daily automated backups
- Application files: Version control
- Environment configs: Secure storage

## Support

For issues or questions:
1. Check the application logs
2. Verify environment configuration
3. Test API endpoints manually
4. Review database connections

## License

MIT License - See LICENSE file for details.
