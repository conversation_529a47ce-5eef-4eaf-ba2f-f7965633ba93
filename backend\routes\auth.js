const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const router = express.Router();
const { getOne, update } = require('../config/database');
const mockDataService = require('../services/mockData');

// Admin login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    let admin = null;

    try {
      // Try database first
      const adminResult = await getOne(
        'SELECT * FROM admins WHERE username = ? AND is_active = TRUE',
        [username]
      );

      if (adminResult.success && adminResult.data) {
        admin = adminResult.data;
      }
    } catch (dbError) {
      console.log('Database not available, using mock data');
    }

    // Fallback to mock data
    if (!admin) {
      const mockResult = mockDataService.getAdminByUsername(username);
      if (mockResult.success && mockResult.data) {
        admin = mockResult.data;
      }
    }

    if (!admin) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, admin.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        id: admin.id,
        username: admin.username,
        type: 'admin'
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Try to update last login in database
    try {
      await update(
        'UPDATE admins SET last_login = NOW() WHERE id = ?',
        [admin.id]
      );
    } catch (dbError) {
      // Ignore database error for mock data
    }

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email
        }
      }
    });
  } catch (error) {
    console.error('Error during admin login:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Verify token
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get current admin data
    const adminResult = await getOne(
      'SELECT id, username, email, is_active FROM admins WHERE id = ? AND is_active = TRUE',
      [decoded.id]
    );

    if (!adminResult.success || !adminResult.data) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    res.json({
      success: true,
      data: {
        admin: adminResult.data,
        token_valid: true
      }
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    
    console.error('Error verifying token:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout (client-side token removal, but we can track it server-side if needed)
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

// Change password
router.post('/change-password', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const { currentPassword, newPassword } = req.body;

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current password and new password are required' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get current admin data
    const adminResult = await getOne(
      'SELECT * FROM admins WHERE id = ? AND is_active = TRUE',
      [decoded.id]
    );

    if (!adminResult.success || !adminResult.data) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const admin = adminResult.data;

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, admin.password_hash);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Hash new password
    const saltRounds = 10;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    const updateResult = await update(
      'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [newPasswordHash, admin.id]
    );

    if (!updateResult.success) {
      return res.status(500).json({ error: 'Failed to update password' });
    }

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    
    console.error('Error changing password:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
