const express = require('express');
const router = express.Router();
const { executeQuery, getOne } = require('../config/database');
const jwt = require('jsonwebtoken');
const mockDataService = require('../services/mockData');

// Middleware to verify admin token
const verifyAdminToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Verify admin still exists and is active
    const adminResult = await getOne(
      'SELECT id, username, email FROM admins WHERE id = ? AND is_active = TRUE',
      [decoded.id]
    );

    if (!adminResult.success || !adminResult.data) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.admin = adminResult.data;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }
    
    console.error('Error verifying admin token:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Apply admin verification to all routes
router.use(verifyAdminToken);

// Dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    try {
      // Try database first
      const queries = {
        totalPCs: 'SELECT COUNT(*) as count FROM pcs',
        availablePCs: 'SELECT COUNT(*) as count FROM pcs WHERE status = "available"',
        inUsePCs: 'SELECT COUNT(*) as count FROM pcs WHERE status = "in_use"',
        maintenancePCs: 'SELECT COUNT(*) as count FROM pcs WHERE status = "maintenance"',
        todayReservations: `
          SELECT COUNT(*) as count
          FROM reservations
          WHERE DATE(start_time) = CURDATE() AND status != 'cancelled'
        `,
        activeReservations: `
          SELECT COUNT(*) as count
          FROM reservations
          WHERE status = 'confirmed' AND NOW() BETWEEN start_time AND end_time
        `,
        upcomingReservations: `
          SELECT COUNT(*) as count
          FROM reservations
          WHERE status = 'confirmed' AND start_time > NOW() AND start_time <= DATE_ADD(NOW(), INTERVAL 24 HOUR)
        `,
        todayRevenue: `
          SELECT COALESCE(SUM(total_amount), 0) as revenue
          FROM reservations
          WHERE DATE(start_time) = CURDATE() AND status IN ('confirmed', 'completed')
        `
      };

      const results = {};

      for (const [key, query] of Object.entries(queries)) {
        const result = await getOne(query);
        if (result.success) {
          results[key] = result.data.count || result.data.revenue || 0;
        } else {
          results[key] = 0;
        }
      }

      // Get recent reservations
      const recentReservationsQuery = `
        SELECT
          r.*,
          p.pc_number,
          p.name as pc_name
        FROM reservations r
        JOIN pcs p ON r.pc_id = p.id
        WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY r.created_at DESC
        LIMIT 10
      `;

      const recentReservations = await executeQuery(recentReservationsQuery);

      // Get PC status overview
      const pcStatusQuery = `
        SELECT
          p.*,
          CASE
            WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND NOW() BETWEEN r.start_time AND r.end_time
            THEN 'in_use'
            WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND r.start_time > NOW()
            THEN 'reserved'
            ELSE p.status
          END as current_status,
          r.customer_name,
          r.start_time,
          r.end_time
        FROM pcs p
        LEFT JOIN reservations r ON p.id = r.pc_id
          AND r.status = 'confirmed'
          AND (
            (NOW() BETWEEN r.start_time AND r.end_time) OR
            (r.start_time > NOW() AND r.start_time <= DATE_ADD(NOW(), INTERVAL 24 HOUR))
          )
        ORDER BY p.pc_number
      `;

      const pcStatus = await executeQuery(pcStatusQuery);

      return res.json({
        success: true,
        data: {
          statistics: results,
          recentReservations: recentReservations.success ? recentReservations.data : [],
          pcStatus: pcStatus.success ? pcStatus.data : []
        }
      });
    } catch (dbError) {
      console.log('Database not available, using mock data');
    }

    // Use mock data
    const mockResult = mockDataService.getDashboardStats();
    res.json(mockResult);
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all reservations with filters (admin view)
router.get('/reservations', async (req, res) => {
  try {
    const { 
      status, 
      pc_id, 
      date_from, 
      date_to, 
      customer_name,
      limit = 100, 
      offset = 0 
    } = req.query;
    
    let query = `
      SELECT 
        r.*,
        p.pc_number,
        p.name as pc_name
      FROM reservations r
      JOIN pcs p ON r.pc_id = p.id
      WHERE 1=1
    `;
    const params = [];

    if (status) {
      query += ' AND r.status = ?';
      params.push(status);
    }

    if (pc_id) {
      query += ' AND r.pc_id = ?';
      params.push(pc_id);
    }

    if (date_from) {
      query += ' AND DATE(r.start_time) >= ?';
      params.push(date_from);
    }

    if (date_to) {
      query += ' AND DATE(r.start_time) <= ?';
      params.push(date_to);
    }

    if (customer_name) {
      query += ' AND r.customer_name LIKE ?';
      params.push(`%${customer_name}%`);
    }

    query += ' ORDER BY r.start_time DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const result = await executeQuery(query, params);
    
    if (!result.success) {
      return res.status(500).json({ error: 'Failed to fetch reservations' });
    }

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM reservations r
      JOIN pcs p ON r.pc_id = p.id
      WHERE 1=1
    `;
    const countParams = [];

    if (status) {
      countQuery += ' AND r.status = ?';
      countParams.push(status);
    }

    if (pc_id) {
      countQuery += ' AND r.pc_id = ?';
      countParams.push(pc_id);
    }

    if (date_from) {
      countQuery += ' AND DATE(r.start_time) >= ?';
      countParams.push(date_from);
    }

    if (date_to) {
      countQuery += ' AND DATE(r.start_time) <= ?';
      countParams.push(date_to);
    }

    if (customer_name) {
      countQuery += ' AND r.customer_name LIKE ?';
      countParams.push(`%${customer_name}%`);
    }

    const countResult = await getOne(countQuery, countParams);

    res.json({
      success: true,
      data: result.data,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: countResult.success ? countResult.data.total : 0
      }
    });
  } catch (error) {
    console.error('Error fetching admin reservations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get revenue reports
router.get('/reports/revenue', async (req, res) => {
  try {
    const { period = 'week' } = req.query;
    
    let query;
    let groupBy;
    
    switch (period) {
      case 'day':
        query = `
          SELECT 
            DATE(start_time) as date,
            COUNT(*) as reservations,
            SUM(total_amount) as revenue
          FROM reservations 
          WHERE status IN ('confirmed', 'completed') 
            AND start_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          GROUP BY DATE(start_time)
          ORDER BY date DESC
        `;
        break;
      case 'week':
        query = `
          SELECT 
            YEARWEEK(start_time) as week,
            COUNT(*) as reservations,
            SUM(total_amount) as revenue
          FROM reservations 
          WHERE status IN ('confirmed', 'completed') 
            AND start_time >= DATE_SUB(NOW(), INTERVAL 8 WEEK)
          GROUP BY YEARWEEK(start_time)
          ORDER BY week DESC
        `;
        break;
      case 'month':
        query = `
          SELECT 
            DATE_FORMAT(start_time, '%Y-%m') as month,
            COUNT(*) as reservations,
            SUM(total_amount) as revenue
          FROM reservations 
          WHERE status IN ('confirmed', 'completed') 
            AND start_time >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
          GROUP BY DATE_FORMAT(start_time, '%Y-%m')
          ORDER BY month DESC
        `;
        break;
      default:
        return res.status(400).json({ error: 'Invalid period. Use day, week, or month' });
    }

    const result = await executeQuery(query);
    
    if (!result.success) {
      return res.status(500).json({ error: 'Failed to fetch revenue report' });
    }

    res.json({
      success: true,
      data: {
        period,
        report: result.data
      }
    });
  } catch (error) {
    console.error('Error fetching revenue report:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
