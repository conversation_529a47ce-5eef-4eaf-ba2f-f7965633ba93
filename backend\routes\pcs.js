const express = require('express');
const router = express.Router();
const { executeQuery, getOne, update } = require('../config/database');
const mockDataService = require('../services/mockData');

// Get all PCs with their current status
router.get('/', async (req, res) => {
  try {
    // Try database first, fallback to mock data
    try {
      const query = `
        SELECT
          p.*,
          CASE
            WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND NOW() BETWEEN r.start_time AND r.end_time
            THEN 'in_use'
            WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND r.start_time > NOW()
            THEN 'reserved'
            ELSE p.status
          END as current_status,
          r.id as current_reservation_id,
          r.customer_name,
          r.start_time,
          r.end_time
        FROM pcs p
        LEFT JOIN reservations r ON p.id = r.pc_id
          AND r.status = 'confirmed'
          AND (
            (NOW() BETWEEN r.start_time AND r.end_time) OR
            (r.start_time > NOW() AND r.start_time <= DATE_ADD(NOW(), INTERVAL 24 HOUR))
          )
        ORDER BY p.pc_number
      `;

      const result = await executeQuery(query);

      if (result.success) {
        return res.json({
          success: true,
          data: result.data
        });
      }
    } catch (dbError) {
      console.log('Database not available, using mock data');
    }

    // Use mock data
    const mockResult = mockDataService.getAllPCs();
    res.json({
      success: true,
      data: mockResult.data
    });
  } catch (error) {
    console.error('Error fetching PCs:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get a specific PC by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT 
        p.*,
        CASE 
          WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND NOW() BETWEEN r.start_time AND r.end_time 
          THEN 'in_use'
          WHEN r.id IS NOT NULL AND r.status = 'confirmed' AND r.start_time > NOW() 
          THEN 'reserved'
          ELSE p.status
        END as current_status,
        r.id as current_reservation_id,
        r.customer_name,
        r.start_time,
        r.end_time
      FROM pcs p
      LEFT JOIN reservations r ON p.id = r.pc_id 
        AND r.status = 'confirmed' 
        AND (
          (NOW() BETWEEN r.start_time AND r.end_time) OR 
          (r.start_time > NOW() AND r.start_time <= DATE_ADD(NOW(), INTERVAL 24 HOUR))
        )
      WHERE p.id = ?
    `;

    const result = await getOne(query, [id]);
    
    if (!result.success) {
      return res.status(500).json({ error: 'Failed to fetch PC' });
    }

    if (!result.data) {
      return res.status(404).json({ error: 'PC not found' });
    }

    res.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    console.error('Error fetching PC:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get PC availability for a specific time range
router.get('/:id/availability', async (req, res) => {
  try {
    const { id } = req.params;
    const { start_time, end_time } = req.query;

    if (!start_time || !end_time) {
      return res.status(400).json({ error: 'start_time and end_time are required' });
    }

    // Check if PC exists
    const pcResult = await getOne('SELECT * FROM pcs WHERE id = ?', [id]);
    if (!pcResult.success || !pcResult.data) {
      return res.status(404).json({ error: 'PC not found' });
    }

    // Check for conflicting reservations
    const conflictQuery = `
      SELECT COUNT(*) as conflicts
      FROM reservations 
      WHERE pc_id = ? 
        AND status IN ('confirmed', 'pending')
        AND (
          (start_time <= ? AND end_time > ?) OR
          (start_time < ? AND end_time >= ?) OR
          (start_time >= ? AND end_time <= ?)
        )
    `;

    const conflictResult = await getOne(conflictQuery, [
      id, start_time, start_time, end_time, end_time, start_time, end_time
    ]);

    if (!conflictResult.success) {
      return res.status(500).json({ error: 'Failed to check availability' });
    }

    const isAvailable = conflictResult.data.conflicts === 0 && pcResult.data.status !== 'maintenance';

    res.json({
      success: true,
      data: {
        pc_id: id,
        start_time,
        end_time,
        available: isAvailable,
        pc_status: pcResult.data.status,
        conflicts: conflictResult.data.conflicts
      }
    });
  } catch (error) {
    console.error('Error checking PC availability:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update PC status (admin only - will add auth middleware later)
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['available', 'in_use', 'reserved', 'maintenance'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const result = await update(
      'UPDATE pcs SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );

    if (!result.success) {
      return res.status(500).json({ error: 'Failed to update PC status' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'PC not found' });
    }

    // Get updated PC data
    const updatedPC = await getOne('SELECT * FROM pcs WHERE id = ?', [id]);
    
    // Broadcast update to all connected clients
    if (global.broadcastPCUpdate && updatedPC.success) {
      global.broadcastPCUpdate(updatedPC.data);
    }

    res.json({
      success: true,
      message: 'PC status updated successfully',
      data: updatedPC.data
    });
  } catch (error) {
    console.error('Error updating PC status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
